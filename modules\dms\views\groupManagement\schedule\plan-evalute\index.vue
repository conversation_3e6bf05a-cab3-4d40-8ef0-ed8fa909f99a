<!-- 计划管理——冲刺评价 -->
<template>
    <div class="add-plan-container">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button type="primary" @click="save('草稿')">保存</el-button>
            <el-button type="primary" @click="save('审核中')">提交</el-button>
            <el-button @click="handleReturn" type="primary">返回</el-button>
        </div>
        <div>
            <!-- 基本信息 -->
            <formula-title :title="basicTitle"></formula-title>
            <plan-card :team-info="basicForm"></plan-card>

            <!--  需求任务 -->
            <formula-title :title="planTitle"></formula-title>
            <NestedPlanInfoEvaluateList
                :list.sync="demandList"
                :saveFlag="saveFlag"
                @success="getPlanListData"
            ></NestedPlanInfoEvaluateList>

            <!--  工时负载率 -->
            <formula-title :title="worktimeTitle"></formula-title>
            <div class="chart-box">
                <el-card class="chart-right">
                    <worktime-chart shadow="never" ref="worktimeRef" :info="workTimeChartInfo"></worktime-chart>
                </el-card>
            </div>

            <!--  冲刺评价 -->
            <formula-title :title="evaluationTitle"></formula-title>
            <el-form
                :model="evalutionForm"
                ref="evalutionForm"
                :rules="evalutionRules"
                label-width="100px"
                class="demo-evalutionForm"
            >
                <el-form-item label="冲刺结果" prop="selfResult">
                    <el-radio-group v-model="evalutionForm.selfResult" @change="handleSelfResultChange">
                        <el-radio label="成功"></el-radio>
                        <el-radio label="失败"></el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="说明" prop="selfDesc">
                    <el-input
                        type="textarea"
                        v-model="evalutionForm.selfDesc"
                        maxlength="500"
                        placeholder="说明失败原因"
                    ></el-input>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
import formulaTitle from 'dms/views/groupManagement/schedule/components/formulaTitle.vue';
import worktimeChart from 'dms/views/groupManagement/schedule/components/worktimeChart';
import moment from 'moment';
import NestedPlanInfoEvaluateList from './components/NestedPlanInfoEvaluateList.vue';
import PlanCard from 'dms/views/groupManagement/schedule/components/planCard.vue';

export default {
    name: 'Planevalute',
    components: { formulaTitle, worktimeChart, NestedPlanInfoEvaluateList, PlanCard },
    props: {},
    data() {
        return {
            // 内部维护的id，这个页面新建的时候一开始就需要id
            innerId: '',
            // 标题
            basicTitle: '基本信息',
            planTitle: '需求任务',
            worktimeTitle: '工时负载率',
            evaluationTitle: '自评',
            // 基本信息表单数据
            basicForm: {
                dateRange: [],
                periodType: '两周',
                planName: '',
                workDays: 0,
                teamName: '',
                teamLeader: '',
                submitDate: ''
            },
            saveFlag: 0,
            demandList: [],
            isSaving: false,
            // 工时负载率echarts图
            workTimeChartInfo: {},
            currentStatus: '',
            evalutionForm: {
                selfResult: '',
                selfDesc: ''
            }
        };
    },
    computed: {
        // 自评表单验证规则
        evalutionRules() {
            return {
                selfResult: [{ required: true, message: '请选择冲刺结果', trigger: 'change' }],
                selfDesc: [{ validator: this.validateSelfDesc, trigger: 'blur' }]
            };
        },
        startDate() {
            return this.basicForm.dateRange && this.basicForm.dateRange[0] ? this.basicForm.dateRange[0] : null;
        },
        endDate() {
            return this.basicForm.dateRange && this.basicForm.dateRange[1] ? this.basicForm.dateRange[1] : null;
        },
        // 选中的团队
        groupValue() {
            return this.$store.state.dms.group;
        },
        groupOptions() {
            return this.$store.state.dms.groupOption;
        }
    },
    async mounted() {
        if (this.$route?.query?.id) {
            this.innerId = this.$route?.query?.id;
            this.getBaseInfo();
            this.getTableList();
            this.getResourceLoadData();
        }
    },
    methods: {
        handleReturn() {
            this.$router.back();
        },
        /**
         * 获取基本信息
         */
        async getBaseInfo() {
            try {
                const params = {
                    id: this.innerId
                };
                const api = this.$service.dms.plan.getPlanBaseInfo;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                const { startDate, endDate } = res.data;
                this.basicForm = { ...this.basicForm, ...res.data };
                this.basicForm.dateRange = [startDate, endDate];

                // 设置提交日期为当前日期
                this.basicForm.submitDate = moment().format('YYYY-MM-DD');
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        },
        /**
         * 获取需求任务、团队活动、问题修复的表格数据
         */
        async getTableList() {
            try {
                const params = {
                    planId: this.innerId
                };
                const api = this.$service.dms.plan.getPlanTaskList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.demandList = res.data.storyTaskList.map((i) => ({
                    ...i,
                    isFinished: '是',
                    reasonDesc: ''
                }));
            } catch (error) {
                console.error('查询需求列表失败:', error);
            }
        },
        save(status) {
            if (!this.innerId) return;

            // 验证自评表单
            this.$refs.evalutionForm.validate((valid) => {
                if (!valid) {
                    this.$message.warning('请完善自评信息');
                    return;
                }

                // 验证需求任务的原因说明
                const invalidDemands = this.demandList.filter(
                    (item) => item.isFinished === '否' && (!item.reasonDesc || item.reasonDesc.trim() === '')
                );

                if (invalidDemands.length > 0) {
                    this.$message.warning('请填写未达成目标的原因说明');
                    return;
                }

                this.currentStatus = status;
                // 设置保存标志
                this.isSaving = true;
                // 触发子组件发送数据
                this.saveFlag += 1;
            });
        },
        getPlanListData(value) {
            this.demandList = value;

            // 如果是在保存过程中，执行保存逻辑
            if (this.isSaving) {
                this.isSaving = false;
                this.performSave();
            }
        },
        async performSave() {
            await this.saveFinalEvalution();
            if (this.currentStatus === '审核中') {
                this.submitFinalEvalution();
            } else {
                this.$message.success('保存成功');
            }
        },
        async saveFinalEvalution() {
            try {
                const params = {
                    planId: this.innerId,
                    ...this.evalutionForm,
                    statusVo: this.demandList.map((i) => ({
                        storyId: i.storyId,
                        isFinished: i.isFinished,
                        reasonDesc: i.reasonDesc
                    }))
                };

                const api = this.$service.dms.plan.saveFinalEvaluation;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                return Promise.resolve();
            } catch (error) {
                console.error('查询需求列表失败:', error);
                return Promise.reject();
            }
        },
        async submitFinalEvalution() {
            try {
                const params = {
                    planId: this.innerId,
                    projectId: this.groupValue,
                    ...this.evalutionForm
                };

                const api = this.$service.dms.plan.submitFinalEvaluation;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                this.$message.success('提交成功');
                this.$router.back();
                return Promise.resolve();
            } catch (error) {
                console.error('查询需求列表失败:', error);
                return Promise.reject();
            }
        },
        /**
         * 获取工时负载率数据
         */
        async getResourceLoadData() {
            try {
                const params = {
                    planId: this.innerId
                };

                const api = this.$service.dms.plan.getPlanWorkHourChart;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                this.workTimeChartInfo = res.data;
                return Promise.resolve();
            } catch (error) {
                console.error('查询需求列表失败:', error);
                return Promise.reject();
            }
        },
        // 验证说明字段
        validateSelfDesc(rule, value, callback) {
            if (this.evalutionForm.selfResult === '失败') {
                if (!value || value.trim() === '') {
                    callback(new Error('请输入失败原因'));
                } else {
                    callback();
                }
            } else {
                callback();
            }
        },
        // 处理冲刺结果变化
        handleSelfResultChange() {
            this.$nextTick(() => {
                // 重新验证说明字段
                this.$refs.evalutionForm.validateField('selfDesc');
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.add-plan-container {
    padding: 10px 20px 15px 20px;
}
.sprint-btn {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    padding: 10px 10px 10px 0px;
    border-bottom: 1px solid #dcdfe6;
}
.chart-box {
    width: 100%;
    height: auto; 
    display: flex;
    justify-content: space-between;
}
.clearfix {
    height: 30px;
}
.chart-right {
    width: 100%;
    height: 100%;
}
.el-card {
    padding: 10px !important;
}
::v-deep .el-card__header {
    height: 30px !important;
}
.load-rate-item {
    border-bottom: 1px solid #dcdfe6;
}

/* 团队活动样式 */
.team-activity-container {
    margin-bottom: 20px;
}

/* 问题修复样式 */
.problem-fix-container {
    margin-bottom: 20px;
}
</style>
