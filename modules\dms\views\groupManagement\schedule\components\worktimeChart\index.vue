<!-- 工时负载率echarts图 -->
<template>
    <div class="worktime-chart-container">
        <el-card class="chart-left" shadow="never">
            <div slot="header" class="clearfix">
                <span>汇总</span>
            </div>
            <div class="load-rate-item">
                <p>{{ loadRateData.over120.label }}</p>
                <p>人数: {{ loadRateData.over120.count }}</p>
                <p>具体人员: {{ loadRateData.over120.persons }}</p>
            </div>
            <div class="load-rate-item">
                <el-button
                    v-if="showEditExpection"
                    class="extra-info-button"
                    type="primary"
                    @click="showExceptionDialog"
                    >异常说明</el-button
                >
                <p>{{ loadRateData.under80.label }}</p>
                <p>人数: {{ loadRateData.under80.count }}</p>
                <p>具体人员: {{ loadRateData.under80.persons }}</p>
            </div>
        </el-card>

        <!-- 异常说明弹窗 -->
        <ExceptionDialog
            :visible.sync="exceptionDialogVisible"
            :under80-persons="under80PersonsList"
            @save-success="handleExceptionSaveSuccess"
        />
        <div class="chartTwo" ref="chartMain" :style="{ height: chartHeight + 'px' }"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
import ExceptionDialog from './ExceptionDialog.vue';

export default {
    components: {
        ExceptionDialog
    },
    props: {
        view: {
            type: String,
            default: 'day'
        },
        info: {
            type: Object,
            default: () => ({})
        },
        // 是否展示异常说明按钮
        showEditExpection: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loadRateData: {
                over120: {
                    label: '负载率>120%',
                    count: 0,
                    persons: ''
                },
                under80: {
                    label: '负载率<80%',
                    count: 0,
                    persons: ''
                }
            },
            exceptionDialogVisible: false,
            under80PersonsList: []
        };
    },
    computed: {
        chartHeight() {
            const heightPerPerson = 57;
            const minHeight = 400;
            const length = this.info?.loadList?.length || 0;
            // 计算所需高度
            let calculatedHeight = length * heightPerPerson;

            calculatedHeight = Math.max(minHeight, calculatedHeight);

            return calculatedHeight || 0;
        }
    },
    watch: {
        info: {
            handler(newVal) {
                if (newVal) {
                    // 使用 $nextTick 确保在 DOM 更新之后再渲染图表
                    // 这样 ECharts 才能获取到正确的容器高度
                    this.$nextTick(() => {
                        this.twoChart();
                    });
                }
            },
            deep: true
        }
    },
    methods: {
        /**
         * 显示异常说明弹窗
         */
        showExceptionDialog() {
            if (this.under80PersonsList.length === 0) {
                this.$message.warning('当前没有负载率<80%的人员');
                return;
            }
            this.exceptionDialogVisible = true;
        },

        /**
         * 处理异常说明保存成功事件
         * @param {Array} updatedExceptions 更新后的异常说明数据
         */
        handleExceptionSaveSuccess(updatedExceptions) {
            // 更新本地数据中的异常说明
            updatedExceptions.forEach((exception) => {
                const person = this.under80PersonsList.find((p) => p.id === exception.id);
                if (person) {
                    person.loadDesc = exception.loadDesc;
                }

                // 同时更新info.loadList中的数据，确保图表重新渲染时能显示最新的异常说明
                if (this.info && this.info.loadList) {
                    const loadListPerson = this.info.loadList.find(
                        (p) => (p.id && p.id === exception.id) || p.memberName === exception.personName
                    );
                    if (loadListPerson) {
                        loadListPerson.loadDesc = exception.loadDesc;
                    }
                }
            });

            // 重新渲染图表以显示最新的异常说明
            this.twoChart();

            // 触发父组件重新获取数据或更新显示
            this.$emit('exception-updated');
        },

        // 计算负载率汇总数据
        calculateLoadRateSummary(dataList) {
            const over120 = dataList.filter((item) => item.loadRate > 120);
            const under80 = dataList.filter((item) => item.loadRate < 80);

            // 保存负载率<80%的人员列表，用于异常说明弹窗，包含已有的异常说明
            this.under80PersonsList = under80.map((item) => ({
                id: item.executionPlanId,
                name: item.memberName,
                account: item.memberAccount,
                load: item.load,
                // 将已有的异常说明传递给弹窗
                loadDesc: item.loadDesc || ''
            }));

            this.loadRateData = {
                over120: {
                    label: '负载率>120%',
                    count: over120.length,
                    persons: over120.map((item) => item.memberName).join('、') || '无'
                },
                under80: {
                    label: '负载率<80%',
                    count: under80.length,
                    persons: under80.map((item) => item.memberName).join('、') || '无'
                }
            };
        },

        // eslint-disable-next-line max-lines-per-function
        async twoChart() {
            if (Object.keys(this.info).length === 0) return;

            const chartDom = this.$refs.chartMain;
            const myChart = echarts.init(chartDom);
            myChart.resize();

            const { oneTheoryTime, twoTheoryTime, loadList } = this.info;

            // 根据新的数据结构处理数据
            const dataWithLoadRate = loadList.map((item) => ({
                ...item,
                // 使用接口返回的 load 字段作为负载率（已经是百分比形式）
                loadRate: item.load,
                // 保留异常说明信息
                loadDesc: item.loadDesc || ''
            }));

            // 计算汇总数据
            this.calculateLoadRateSummary(dataWithLoadRate);

            // 提取排序后的数据，使用新的字段名
            const sortedNames = dataWithLoadRate.map((item) => item.memberName);

            // 预估工时数据
            const sortedTeamTimes = dataWithLoadRate.map((item) => item.estimate);
            const sortedExternalTimes = dataWithLoadRate.map((item) => item.otherEstimate);
            const sortedLeaveTimes = dataWithLoadRate.map((item) => item.leaveTime);
            const sortedIdleTimes = dataWithLoadRate.map((item) => ({
                value: item.idleTime,
                loadDesc: item.loadDesc
            }));
            const sortedLoadRates = dataWithLoadRate.map((item) => `${Math.round(item.load)}`);

            // 实际消耗工时数据
            const sortedConsumedTimes = dataWithLoadRate.map((item) => item.consumed);
            const sortedOtherConsumedTimes = dataWithLoadRate.map((item) => item.otherConsumed);
            const sortedRealIdleTimes = dataWithLoadRate.map((item) => item.realIdleTime);
            const sortedRealLoadRates = dataWithLoadRate.map((item) => `${Math.round(item.realLoad)}`);

            // 设置图表选项
            const option = {
                tooltip: {
                    trigger: 'item',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    x: 20,
                    y: 40,
                    x2: 80,
                    y2: 0,
                    containLabel: true
                },
                legend: {
                    data: [
                        '本团队预估工时',
                        '团队外预估工时',
                        '请假时间',
                        '预估空闲工时',
                        '本团队实际工时',
                        '团队外实际工时',
                        '实际空闲工时',
                        '负载率',
                        '实际负载率'
                    ],
                    textStyle: {
                        fontSize: 12
                    }
                },
                xAxis: {
                    type: 'value',
                    show: true,
                    axisTick: {
                        show: false,
                        lineStyle: {
                            color: '#ECEDF0',
                            width: 1,
                            type: 'solid'
                        }
                    }
                },
                yAxis: {
                    type: 'category',
                    data: sortedNames,
                    axisLabel: {
                        fontSize: 12,
                        textStyle: {
                            color: '#333'
                        }
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#ECEDF0',
                            width: 1,
                            type: 'solid'
                        }
                    }
                },
                series: [
                    // 预估工时系列
                    {
                        name: '本团队预估工时',
                        type: 'bar',
                        stack: 'estimate',
                        barCategoryGap: '20%',
                        label: {
                            show: true,
                            formatter(params) {
                                return params.value === 0 ? '' : params.value;
                            }
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#1E90FF'
                        },
                        barWidth: 16,
                        data: sortedTeamTimes,
                        markLine: {
                            symbol: 'none',
                            data: (() => {
                                const lines = [];
                                // 只有当理论值存在时才显示
                                if (oneTheoryTime) {
                                    lines.push({
                                        xAxis: oneTheoryTime,
                                        lineStyle: {
                                            color: 'red',
                                            type: 'solid',
                                            width: 2
                                        },
                                        label: {
                                            show: true,
                                            position: 'end',
                                            formatter: `理论 ${oneTheoryTime}`,
                                            offset: [0, -15]
                                        }
                                    });
                                }

                                // 只有当两个理论值都存在且不相等时才显示第二条
                                if (twoTheoryTime && twoTheoryTime !== oneTheoryTime) {
                                    lines.push({
                                        // xAxis: twoTheoryTime,
                                        xAxis: 100,
                                        lineStyle: {
                                            color: 'orange',
                                            type: 'dashed',
                                            width: 2
                                        },
                                        label: {
                                            show: true,
                                            position: 'end',
                                            formatter: `理论 ${twoTheoryTime}`,
                                            offset: [0, 15]
                                        }
                                    });
                                }
                                console.log(lines, 'lines');

                                return lines;
                            })()
                        }
                    },
                    {
                        name: '团队外预估工时',
                        type: 'bar',
                        stack: 'estimate',
                        label: {
                            show: true,
                            formatter(params) {
                                return params.value === 0 ? '' : params.value;
                            }
                        },
                        itemStyle: {
                            color: '#87CEFA'
                        },
                        barWidth: 16,
                        data: sortedExternalTimes
                    },
                    {
                        name: '请假时间',
                        type: 'bar',
                        stack: 'estimate',
                        label: {
                            show: true,
                            formatter(params) {
                                return params.value === 0 ? '' : params.value;
                            }
                        },
                        itemStyle: {
                            color: '#00ff00'
                        },
                        barWidth: 16,
                        data: sortedLeaveTimes
                    },
                    {
                        name: '预估空闲工时',
                        type: 'bar',
                        stack: 'estimate',
                        label: {
                            show: true,
                            formatter(params) {
                                const { value } = params.value;
                                return value === 0 ? '' : value;
                            }
                        },
                        itemStyle: {
                            color: '#FF8C00'
                        },
                        barWidth: 16,
                        data: sortedIdleTimes,
                        tooltip: {
                            formatter(params) {
                                let result = `${params.marker}${params.seriesName}: ${params.data.value}`;
                                if (params.data.loadDesc) {
                                    result += `<br/>异常说明: ${params.data.loadDesc}`;
                                }
                                return result;
                            }
                        }
                    },
                    // 实际消耗工时系列
                    {
                        name: '本团队实际工时',
                        type: 'bar',
                        stack: 'actual',
                        label: {
                            show: true,
                            formatter(params) {
                                return params.value === 0 ? '' : params.value;
                            }
                        },
                        emphasis: {
                            focus: 'series'
                        },
                        itemStyle: {
                            color: '#4169E1'
                        },
                        barWidth: 16,
                        data: sortedConsumedTimes
                    },
                    {
                        name: '团队外实际工时',
                        type: 'bar',
                        stack: 'actual',
                        label: {
                            show: true,
                            formatter(params) {
                                return params.value === 0 ? '' : params.value;
                            }
                        },
                        itemStyle: {
                            color: '#6495ED'
                        },
                        barWidth: 16,
                        data: sortedOtherConsumedTimes
                    },
                    {
                        name: '实际空闲工时',
                        type: 'bar',
                        stack: 'actual',
                        label: {
                            show: true,
                            formatter(params) {
                                return params.value === 0 ? '' : params.value;
                            }
                        },
                        itemStyle: {
                            color: '#FF6347'
                        },
                        barWidth: 16,
                        data: sortedRealIdleTimes
                    },
                    {
                        name: '负载率',
                        type: 'bar',
                        stack: 'estimate',
                        barWidth: 16,
                        label: {
                            show: true,
                            position: 'right',
                            color: 'red',
                            fontWeight: 'bold',
                            formatter: (params) => `${sortedLoadRates[params.dataIndex]}%`
                        },
                        itemStyle: {
                            color: 'transparent'
                        },
                        data: sortedTeamTimes.map(() => 0)
                    },
                    {
                        name: '实际负载率',
                        type: 'bar',
                        stack: 'actual',
                        barWidth: 16,
                        label: {
                            show: true,
                            position: 'right',
                            color: 'blue',
                            fontWeight: 'bold',
                            formatter: (params) => `${sortedRealLoadRates[params.dataIndex]}%`
                        },
                        itemStyle: {
                            color: 'transparent'
                        },
                        data: sortedConsumedTimes.map(() => 0)
                    }
                ]
            };
            myChart.setOption(option);
        }
    }
};
</script>

<style scoped lang="scss">
.worktime-chart-container {
    display: flex;
    gap: 16px;
    height: 100%;
}
.sprint-btn {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    padding: 10px 10px 10px 0px;
    border-bottom: 1px solid #dcdfe6;
}
.chart-left {
    width: 30%;
    height: 100%;
}
.load-rate-item {
    position: relative;
    font-weight: bold;
    border-bottom: 1px solid #dcdfe6;
}

.extra-info-button {
    position: absolute;
    right: 0;
    top: 0;
}
.chartTwo {
    flex: 1;
    height: 100%;
}
</style>
