<template>
    <div class="problem-fix">
        <div class="add-problem-container">
            <el-button class="add-problem" size="small" type="primary" @click="relateProblem"> 关联bug</el-button>
        </div>
        <el-table
            :data="problemData"
            class="dms-table"
            style="width: 100%"
            :header-cell-style="{ background: '#5470c6', color: '#fff', textAlign: 'center' }"
            :cell-style="{ textAlign: 'center' }"
        >
            <el-table-column prop="taskName" label="任务名称" min-width="200">
                <template slot="header"> <red-star></red-star>任务名称 </template>
                <template slot-scope="scope">
                    <el-input
                        v-model="scope.row.taskName"
                        size="small"
                        style="width: 100%"
                        :class="{ 'is-error': !scope.row.taskName }"
                        @blur="validateField(scope.row, 'taskName')"
                    />
                </template>
            </el-table-column>
            <el-table-column prop="taskType" label="任务类型" width="120">
                <template slot="header"> <red-star></red-star>任务类型 </template>
                <template slot-scope="scope">
                    <el-select
                        v-model="scope.row.taskType"
                        placeholder="请选择"
                        size="small"
                        style="width: 100%"
                        :class="{ 'is-error': !scope.row.taskType }"
                        @change="validateField(scope.row, 'taskType')"
                    >
                        <el-option
                            v-for="item in dict.taskTypeData"
                            :value="item.value"
                            :key="item.value"
                            :label="item.label"
                        ></el-option>
                    </el-select>
                </template>
            </el-table-column>
            <el-table-column prop="assignedTo" label="责任人" width="120">
                <template slot="header"> <red-star></red-star>责任人 </template>
                <template slot-scope="scope">
                    <people-selector
                        v-model="scope.row.assignedTo"
                        size="small"
                        :isMultipled="false"
                        :isRemote="false"
                        :options="groupMembers"
                        @change="validateField(scope.row, 'assignedTo')"
                    ></people-selector>
                </template>
            </el-table-column>
            <el-table-column prop="startDate" label="计划开始日期" width="140">
                <template slot="header"> <red-star></red-star>计划开始日期 </template>
                <template slot-scope="scope">
                    <el-date-picker
                        v-model="scope.row.startDate"
                        type="date"
                        placeholder="选择日期"
                        size="small"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        style="width: 100%"
                        :class="{ 'is-error': !scope.row.startDate }"
                        @change="validateField(scope.row, 'startDate')"
                    />
                </template>
            </el-table-column>
            <el-table-column prop="endDate" label="计划完成日期" width="140">
                <template slot="header"> <red-star></red-star>计划完成日期 </template>
                <template slot-scope="scope">
                    <el-date-picker
                        v-model="scope.row.endDate"
                        type="date"
                        placeholder="选择日期"
                        size="small"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd"
                        style="width: 100%"
                        :class="{ 'is-error': !scope.row.endDate }"
                        @change="validateField(scope.row, 'endDate')"
                    />
                </template>
            </el-table-column>
            <el-table-column prop="estimate" label="预计工时" width="100">
                <template slot="header"> <red-star></red-star>预计工时 </template>
                <template slot-scope="scope">
                    <el-input-number
                        v-model="scope.row.estimate"
                        :min="0"
                        size="small"
                        :controls="false"
                        style="width: 100%"
                        :class="{ 'is-error': !scope.row.estimate || scope.row.estimate <= 0 }"
                        @change="validateField(scope.row, 'estimate')"
                    />
                </template>
            </el-table-column>
            <el-table-column prop="fromBug" label="bugID" width="100"> </el-table-column>
            <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                    <el-button size="small" type="text" @click="removeProblem(scope.$index)">删除</el-button>
                </template>
            </el-table-column>
        </el-table>
        <RelateBugDialog :visible.sync="dialogVisible" @confirm="handleRelateBug" />
    </div>
</template>

<script>
import RelateBugDialog from './RelateBugDialog';
import RedStar from 'dms/components/RedStar.vue';
import PeopleSelector from 'dms/components/PeopleSelector';
import dict from 'dms/constant/dict.js';
import moment from 'moment';

export default {
    name: 'ProblemFix',
    components: {
        RelateBugDialog,
        RedStar,
        PeopleSelector
    },
    props: {
        list: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            dialogVisible: false,
            dict,
            problemData: []
        };
    },
    computed: {
        groupMembers() {
            return this.$store.state.dms.groupMembers;
        }
    },
    watch: {
        list: {
            handler(newVal) {
                this.problemData = [...newVal];
            },
            immediate: true,
            deep: true
        }
    },
    methods: {
        // 关联bug
        relateProblem() {
            this.dialogVisible = true;
        },

        // 删除问题
        async removeProblem(index) {
            await this.$confirm('确认删除该项吗?', '提示', { type: 'warning' });
            this.problemData.splice(index, 1);
        },

        // 校验字段
        validateField(row, field) {
            if (field === 'estimate') {
                return row[field] && row[field] > 0;
            }
            return row[field] && row[field].toString().trim() !== '';
        },

        // 校验所有数据
        validateAllData() {
            const requiredFields = ['taskName', 'taskType', 'assignedTo', 'startDate', 'endDate', 'estimate'];
            let isValid = true;

            this.problemData.forEach((row) => {
                requiredFields.forEach((field) => {
                    if (!this.validateField(row, field)) {
                        isValid = false;
                    }
                });
            });

            return isValid;
        },
        handleRelateBug(value) {
            const problemList = value.map((i) => ({
                taskName: `[bug处理]${i.title}`,
                taskType: '线上问题修复',
                assignedTo: i.assignedTo,
                startDate: moment().format('yyyy-MM-DD') || '',
                endDate: i.deadLine || '',
                estimate: 4,
                fromBug: i.id
            }));

            this.problemData.push(...problemList);
        },

        // 获取表格数据的方法
        getTableData() {
            return this.problemData;
        }
    }
};
</script>

<style lang="scss" scoped>
.add-problem-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 5px;
}

::v-deep .is-error .el-input__inner,
::v-deep .is-error .el-input-number__input,
::v-deep .is-error .el-select .el-input__inner {
    border-color: #f56c6c;
}
</style>
