<!-- 计划管理——冲刺评价 -->
<template>
    <div class="add-plan-container">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button type="primary" @click="save()">提交</el-button>
            <el-button @click="handleReturn" type="primary">返回</el-button>
        </div>
        <div>
            <PlanDetail />
            <formula-title title="自评">
                <el-form :model="selfEvalutionForm" ref="selfEvalutionForm" label-width="100px">
                    <el-form-item label="冲刺结果" prop="sprintResult">
                        <el-radio-group v-model="selfEvalutionForm.sprintResult" :disabled="true">
                            <el-radio label="成功"></el-radio>
                            <el-radio label="失败"></el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="说明" prop="auditDesc">
                        <el-input
                            type="textarea"
                            v-model="selfEvalutionForm.auditDesc"
                            maxlength="500"
                            placeholder="说明失败原因"
                            :disabled="true"
                        ></el-input>
                    </el-form-item>
                </el-form>
            </formula-title>

            <!--  冲刺评价 -->
            <formula-title title="审核"></formula-title>
            <el-form :model="evalutionForm" ref="evalutionForm" :rules="evalutionRules" label-width="100px">
                <el-form-item label="冲刺结果" prop="sprintResult">
                    <el-radio-group v-model="evalutionForm.sprintResult">
                        <el-radio label="成功"></el-radio>
                        <el-radio label="失败"></el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="说明" prop="auditDesc">
                    <el-input
                        type="textarea"
                        v-model="evalutionForm.auditDesc"
                        maxlength="500"
                        placeholder="说明失败原因"
                    ></el-input>
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script>
import formulaTitle from 'dms/views/groupManagement/schedule/components/formulaTitle.vue';
import PlanDetail from 'dms/views/groupManagement/components/PlanDetail';

export default {
    name: 'Planevalute',
    components: { formulaTitle, PlanDetail },
    props: {},
    data() {
        return {
            // 内部维护的id，这个页面新建的时候一开始就需要id
            innerId: '',
            // 自评表单
            selfEvalutionForm: {
                sprintResult: '',
                auditDesc: ''
            },
            // 审核表单
            evalutionForm: {
                sprintResult: '',
                auditDesc: ''
            }
        };
    },
    computed: {
        // 自评表单验证规则
        evalutionRules() {
            return {
                sprintResult: [{ required: true, message: '请选择冲刺结果', trigger: 'change' }],
                auditDesc: [{ validator: this.validateSelfDesc, trigger: 'blur' }]
            };
        },
        // 选中的团队
        groupValue() {
            return this.$store.state.dms.group;
        },
        groupOptions() {
            return this.$store.state.dms.groupOption;
        }
    },
    async mounted() {
        if (this.$route?.query?.id) {
            this.innerId = this.$route?.query?.id;
        }
    },
    methods: {
        handleReturn() {
            this.$router.back();
        },
        validateSelfDesc(rule, value, callback) {
            if (this.evalutionForm.sprintResult === '失败') {
                if (!value || value.trim() === '') {
                    callback(new Error('请输入失败原因'));
                } else {
                    callback();
                }
            } else {
                callback();
            }
        },
        async save() {
            let isValid = false;
            await this.$refs.evalutionForm.validate((valid) => {
                isValid = valid;
            });
            if (!isValid) {
                this.$message.warning('请输入必填项');
                return;
            }
            try {
                const params = {
                    planId: this.innerId,
                    projectId: this.groupValue,
                    ...this.evalutionForm
                };

                const api = this.$service.dms.plan.reviewEvaluation;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                this.$message.success('提交成功');
                this.$router.back();
                return Promise.resolve();
            } catch (error) {
                console.error('查询需求列表失败:', error);
                return Promise.reject();
            }
        },
        /**
         * 获取冲刺评价信息（自评信息）
         */
        async getReviewInfo() {
            try {
                const params = {
                    planId: this.innerId,
                    projectId: this.groupValue,
                    ...this.evalutionForm
                };

                const api = this.$service.dms.plan.reviewEvaluation;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                this.$message.success('提交成功');
                this.$router.back();
                return Promise.resolve();
            } catch (error) {
                console.error('查询需求列表失败:', error);
                return Promise.reject();
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.add-plan-container {
    padding: 10px 20px 15px 20px;
}
.sprint-btn {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    padding: 10px 10px 10px 0px;
    border-bottom: 1px solid #dcdfe6;
}
.chart-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
}
.clearfix {
    height: 30px;
}
.chart-right {
    width: 100%;
    height: 100%;
}
.el-card {
    padding: 10px !important;
}
::v-deep .el-card__header {
    height: 30px !important;
}
.load-rate-item {
    border-bottom: 1px solid #dcdfe6;
}

/* 团队活动样式 */
.team-activity-container {
    margin-bottom: 20px;
}

/* 问题修复样式 */
.problem-fix-container {
    margin-bottom: 20px;
}
</style>
