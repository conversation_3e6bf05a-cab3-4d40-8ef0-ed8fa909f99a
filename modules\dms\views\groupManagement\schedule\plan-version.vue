<template>
    <div class="view">
        <!-- 按钮 -->
        <div class="sprint-btn">
            <el-button type="primary">返回</el-button>
        </div>
        <div>
            <!-- 基本信息 -->
            <formula-title :title="basicTitle"></formula-title>
            <plan-card :teamInfo="teamInfo"></plan-card>
            <!--  计划完详情 -->
            <formula-title :title="planTitle"></formula-title>
            <!-- 审核 -->
            <formula-title :title="examineTitle"></formula-title>
            <el-form :model="ruleForm" ref="ruleForm" label-width="100px">
                <el-form-item label="审核结果:"> 通过 </el-form-item>
                <el-form-item label="审批意见" prop="desc">
                    <el-input
                        type="textarea"
                        v-model="ruleForm.desc"
                        maxlength="500"
                        :rows="3"
                        :disabled="true"
                    ></el-input>
                </el-form-item>
            </el-form>
            <!-- 操作记录 -->
            <formula-title :title="recordTitle"></formula-title>
            <operation-record :collapseItems="collapseItems"></operation-record>
        </div>
    </div>
</template>

<script>
import formulaTitle from './components/formulaTitle.vue';
import planCard from './components/planCard.vue';
import operationRecord from './components/operationRecord.vue';

export default {
    components: { formulaTitle, planCard, operationRecord },
    data() {
        return {
            // 标题
            basicTitle: '基本信息',
            planTitle: '计划详情',
            examineTitle: '审核',
            recordTitle: '操作纪录',
            // 基本信息数据
            teamInfo: {
                teamName: '自动化分拣前端团队',
                teamLeader: '王鑫',
                planName: '自动化分拣前端团队(25.6.6-6.30)',
                planCycle: '2025-06-06 至 2025-06-30',
                availableWorkDays: '20 天',
                version: 'V2',
                submitDate: '2025-06-12'
            },
            ruleForm: {
                desc: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx'
            },
            collapseItems: [
                {
                    name: '1',
                    date: '2025-06-17 17:27:16',
                    operator: '钟振国',
                    status: '创建',
                    opinion: '创建任务初始记录',
                    concept1: '修改内容：最初预计、预计剩余从 12 改为 16',
                    concept2: '版本从 1 改为 2；',
                    concept3: '版本从 2 改为 3；',
                    concept4: '版本从 2 改为 3；'
                },
                {
                    name: '2',
                    date: '2025-06-17 17:35:00',
                    operator: '钟振国',
                    status: '编辑',
                    opinion: '调整任务相关参数及描述',
                    concept1: '修改内容：最初预计、预计剩余从 12 改为 16',
                    concept2: '版本从 1 改为 2；',
                    concept3: '版本从 2 改为 3；',
                    concept4: '版本从 2 改为 3；'
                },
                {
                    name: '3',
                    date: '2025-06-18 16:41:34',
                    operator: '于淼',
                    status: '启动',
                    opinion: '启动计划管理页面开发任务',
                    concept1: '修改内容：最初预计、预计剩余从 12 改为 16',
                    concept2: '版本从 1 改为 2；',
                    concept3: '版本从 2 改为 3；',
                    concept4: '版本从 2 改为 3；'
                },
                {
                    name: '4',
                    date: '2025-06-19 19:06:11',
                    operator: '钟振国',
                    status: '编辑',
                    opinion: '调整任务截止日期和版本',
                    concept1: '修改内容：最初预计、预计剩余从 12 改为 16',
                    concept2: '版本从 1 改为 2；',
                    concept3: '版本从 2 改为 3；',
                    concept4: '版本从 2 改为 3；'
                }
            ]
        };
    },
    mounted() {
        this.$refs.worktimeRef.twoChart(this.rawData);
    },
    methods: {}
};
</script>

<style lang="scss" scoped>
.sprint-btn {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    padding: 10px 10px 10px 0px;
    border-bottom: 1px solid #dcdfe6;
}
.chart-box {
    width: 100%;
    display: flex;
    justify-content: space-between;
}
.chart-left {
    width: 30%;
    height: 100%;
}
.clearfix {
    height: 30px;
}
.chart-right {
    width: 69%;
    height: 100%;
}
.el-card {
    padding: 10px !important;
}
::v-deep .el-card__header {
    height: 30px !important;
}
.load-rate-item {
    border-bottom: 1px solid #dcdfe6;
}
::v-deep .el-tabs__content {
    padding: 3px 0 0 0;
}
</style>
