<!-- 计划管理-变更计划 -->
<template>
    <div style="padding: 10px 20px; position: relative">
        <!-- tab切换 -->
        <div class="sprint-btn">
            <el-tabs v-model="activeName" :before-leave="handleClick">
                <el-tab-pane label="①计划调整" name="planAdjustment">
                    <div>
                        <!-- 基本信息 -->
                        <formula-title :title="basicTitle"></formula-title>
                        <plan-card :team-info="basicForm"></plan-card>

                        <!--  计划详情 -->
                        <formula-title :title="planTitle"></formula-title>
                        <NestedPlanList
                            :list.sync="demandList"
                            :saveFlag="saveFlag"
                            @success="getPlanListData"
                        ></NestedPlanList>

                        <!-- 工时负载率echarts图 -->
                        <formula-title :title="worktimeTitle"></formula-title>
                        <div class="chart-box">
                            <el-card class="chart-right">
                                <worktime-chart
                                    shadow="never"
                                    ref="worktimeRef"
                                    :info="workTimeChartInfo"
                                ></worktime-chart>
                            </el-card>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="②变更申请" name="changeRequest">
                    <change-request-form
                        ref="changeRequestForm"
                        :initial-data="changeForm"
                        :disabled="false"
                    ></change-request-form>
                </el-tab-pane>
            </el-tabs>
        </div>
        <div style="position: absolute; top: 20px; right: 20px">
            <el-button type="primary" @click="handleStepAction">{{
                activeName === 'planAdjustment' ? '下一步' : '上一步'
            }}</el-button>
            <el-button v-if="activeName === 'changeRequest'" type="primary" @click="save('提交')">提交</el-button>
            <el-button type="primary" @click="handleReturn">返回</el-button>
        </div>
    </div>
</template>

<script>
import planCard from '../components/planCard.vue';
import NestedPlanList from 'dms/views/groupManagement/components/nestedPlanList';
import formulaTitle from 'dms/views/groupManagement/schedule/components/formulaTitle.vue';
import worktimeChart from 'dms/views/groupManagement/schedule/components/worktimeChart';
import ChangeRequestForm from 'dms/views/groupManagement/schedule/components/ChangeRequestForm.vue';

export default {
    components: { formulaTitle, planCard, worktimeChart, NestedPlanList, ChangeRequestForm },
    data() {
        return {
            activeName: 'planAdjustment',
            // 内部维护的id
            innerId: '',
            // 标题
            basicTitle: '基本信息',
            planTitle: '计划详情',
            worktimeTitle: '工时负载率',
            changeTitle: '变更信息',
            // 基本信息表单数据
            basicForm: {
                dateRange: [],
                periodType: '两周',
                planName: '',
                workDays: 0
            },
            // 基本信息数据
            saveFlag: 0,
            demandList: [],
            isSaving: false,
            // 工时负载率echarts图
            workTimeChartInfo: {},
            loadRateData: {},
            rawData: [],
            changeForm: {
                changeCause: '',
                changeContent: '',
                changeImpact: '',
                changeRisk: ''
            },

            currentStatus: ''
        };
    },
    computed: {
        startDate() {
            return this.basicForm.dateRange && this.basicForm.dateRange[0] ? this.basicForm.dateRange[0] : null;
        },
        endDate() {
            return this.basicForm.dateRange && this.basicForm.dateRange[1] ? this.basicForm.dateRange[1] : null;
        },
        // 选中的团队
        groupValue() {
            return this.$store.state.dms.group;
        },
        groupOptions() {
            return this.$store.state.dms.groupOption;
        }
    },
    async mounted() {
        // 获取计划ID
        if (this.$route?.query?.id) {
            this.innerId = this.$route?.query?.id;
        }
        await this.cloneNewPlan();
        await this.getBaseInfo();
        await this.getGroupMembers();
        this.getTableList();
        this.getResourceLoadData();
    },
    methods: {
        async handleClick(activeName, oldActiveName) {
            // tab切换前进行保存和校验
            if (oldActiveName === 'changeRequest') {
                // 从变更申请切换到计划调整，需要校验变更申请表单
                const valid = await this.validateChangeForm();
                if (!valid) {
                    return Promise.reject();
                }
                await this.saveChangeInfo();
                this.$message.success('保存成功');
                return Promise.resolve();
            } else if (oldActiveName === 'planAdjustment') {
                // 从计划调整切换到变更申请，保存计划调整数据
                this.save('草稿');
                return Promise.resolve();
            }
        },
        /**
         * 处理步骤操作 - 上一步/下一步
         */
        async handleStepAction() {
            if (this.activeName === 'planAdjustment') {
                // 当前在计划调整页面，点击下一步，先保存然后切换到变更申请
                this.save('草稿');
                this.activeName = 'changeRequest';
            } else {
                // 当前在变更申请页面，点击上一步，需要校验变更申请表单
                const valid = await this.validateChangeForm();
                if (!valid) {
                    return;
                }
                await this.saveChangeInfo();
                this.$message.success('保存成功');
                // 切换到计划调整
                this.activeName = 'planAdjustment';
            }
        },
        /**
         * 克隆新的计划
         */
        async cloneNewPlan() {
            if (!this.innerId) return;
            try {
                const params = {
                    planId: this.innerId
                };
                const api = this.$service.dms.plan.cloneNewPlan;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    this.$router.back();
                    return Promise.reject();
                }
                this.innerId = res.data;
                return Promise.resolve();
            } catch (error) {
                console.error('获取基本信息失败:', error);
                return Promise.reject();
            }
        },
        /**
         * 获取基本信息
         */
        async getBaseInfo() {
            if (!this.innerId) return;
            try {
                const params = {
                    id: this.innerId
                };
                const api = this.$service.dms.plan.getPlanBaseInfo;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                const { startDate, endDate } = res.data;
                this.basicForm = { ...this.basicForm, ...res.data };
                this.basicForm.dateRange = [startDate, endDate];
                // console.log(this.basicForm, 'this.basicForm');
            } catch (error) {
                console.error('获取基本信息失败:', error);
            }
        },
        /**
         * 保存基本信息
         */
        async saveBaseInfo() {
            if (!this.innerId) return Promise.resolve();
            try {
                const params = {
                    id: this.innerId,
                    status: this.currentStatus,
                    ...this.basicForm,
                    startDate: this.basicForm.dateRange[0],
                    endDate: this.basicForm.dateRange[1],
                    ...this.groupOptions,
                    pmAccount: this.groupOptions.pm
                };

                const api = this.$service.dms.plan.savePlanBaseInfo;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                return Promise.resolve();
            } catch (error) {
                console.error('保存基本信息失败:', error);
                return Promise.reject();
            }
        },
        /**
         * 保存基本信息
         */
        async saveChangeInfo() {
            try {
                const changeFormData = this.$refs.changeRequestForm.getFormData();
                const params = { executionPlanId: this.innerId, ...changeFormData };
                const api = this.$service.dms.plan.savePlanChangeInfo;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                return Promise.resolve();
            } catch (error) {
                console.error('保存基本信息失败:', error);
                return Promise.reject();
            }
        },
        /**
         * 获取团队下的人员列表
         */
        async getGroupMembers() {
            if (!this.innerId) return;
            try {
                const params = {
                    planId: this.innerId
                };
                const api = this.$service.dms.plan.getMemebersInGroup;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                const groupMembers = res.data.map((i) => ({ loginName: i.account, employeeName: i.name }));
                this.$store.dispatch('dms/setGroupMembers', groupMembers);
            } catch (error) {
                console.error('获取团队成员失败:', error);
            }
        },
        /**
         * 获取需求任务、团队活动、问题修复的表格数据
         */
        async getTableList() {
            if (!this.innerId) return;
            try {
                const params = {
                    planId: this.innerId
                };
                const api = this.$service.dms.plan.getPlanTaskList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }
                this.demandList = res.data.storyTaskList;
            } catch (error) {
                console.error('获取表格数据失败:', error);
            }
        },
        /**
         * 保存表格数据
         */
        async saveTableList() {
            if (!this.innerId) return Promise.resolve();
            try {
                const params = {
                    projectId: this.groupValue,
                    executionPlanId: this.innerId,
                    storyTaskList: this.demandList,
                    teamTaskList: [],
                    problemTaskList: [],
                    leaveTaskList: []
                };

                const api = this.$service.dms.plan.updatePlanTaskList;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                return Promise.resolve();
            } catch (error) {
                console.error('保存表格数据失败:', error);
                return Promise.reject();
            }
        },
        /**
         * 获取工时负载率数据
         */
        async getResourceLoadData() {
            if (!this.innerId) return;
            try {
                const params = {
                    planId: this.innerId
                };

                const api = this.$service.dms.plan.getPlanWorkHourChart;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return;
                }

                this.workTimeChartInfo = res.data;
                // 更新图表
                if (this.$refs.worktimeRef) {
                    this.$refs.worktimeRef.updateChart(this.workTimeChartInfo);
                }
            } catch (error) {
                console.error('获取工时负载率数据失败:', error);
            }
        },
        /**
         * 保存操作 - 对应原来的上一步/下一步按钮
         * @param {string} status - 保存状态
         */
        async save(status) {
            if (!this.innerId) return;

            // 如果是提交操作，需要校验变更申请表单
            if (status === '提交') {
                const valid = await this.validateChangeForm();
                if (!valid) {
                    return;
                }
                await this.saveChangeInfo();
                await this.submitReview();
            }

            this.currentStatus = status || '草稿';
            // 设置保存标志
            this.isSaving = true;
            // 触发子组件发送数据
            this.saveFlag += 1;
        },
        /**
         * 获取计划列表数据回调
         * @param {Array} value - 计划列表数据
         */
        getPlanListData(value) {
            this.demandList = value;

            // 如果是在保存过程中，执行保存逻辑
            if (this.isSaving) {
                this.isSaving = false;
                this.performSave();
            }
        },
        /**
         * 执行保存
         */
        async performSave() {
            try {
                await this.saveBaseInfo();
                await this.saveTableList();

                // 如果是提交状态，还需要保存变更申请信息
                if (this.currentStatus === '提交') {
                    await this.saveChangeInfo();
                    await this.submitReview();
                }

                this.$message.success('保存成功');
            } catch (error) {
                console.error('保存失败:', error);
            }
        },
        /**
         * 校验变更申请表单
         * @returns {Promise<boolean>} 校验结果
         */
        async validateChangeForm() {
            const valid = await this.$refs.changeRequestForm.validate();
            if (!valid) {
                this.$message.warning('请完善变更申请信息');
                // 如果当前不在变更申请页签，切换到变更申请页签
                if (this.activeName !== 'changeRequest') {
                    this.activeName = 'changeRequest';
                }
            }
            return valid;
        },
        async submitReview() {
            try {
                const params = {
                    planId: this.innerId,
                    projectId: this.groupValue
                };

                const api = this.$service.dms.plan.submitReview;
                const res = await api(params);

                if (res.code !== '0000') {
                    this.$message.error(res.message);
                    return Promise.reject();
                }
                this.$message.success('提交成功');
                this.$router.back();
                return Promise.resolve();
            } catch (error) {
                console.error('查询需求列表失败:', error);
                return Promise.reject();
            }
        },
        /**
         * 返回
         */
        handleReturn() {
            this.$router.back();
        }
    }
};
</script>

<style lang="scss" scoped>
.sprint-btn {
    flex: 1;
    display: flex;
    justify-content: space-between;
    padding: 10px 10px 10px 0px;
}
::v-deep .el-tabs {
    width: 100% !important;
    position: relative;
}
.btn1 {
    position: absolute;
    right: 110px;
    top: 0;
}
.btn2 {
    position: absolute;
    right: 30px;
    top: 0;
}
.chart-box {
    width: 100%;
    min-height: 300px; /* 最小高度保持原有设置 */
    height: auto; /* 允许高度自适应 */
    display: flex;
    justify-content: space-between;
}
.chart-left {
    width: 30%;
    height: 100%;
}
.clearfix {
    height: 30px;
}
.chart-right {
    width: 100%;
    height: 100%;
}
.el-card {
    padding: 10px !important;
}
::v-deep .el-card__header {
    height: 30px !important;
}
.load-rate-item {
    border-bottom: 1px solid #dcdfe6;
}
::v-deep .el-tabs__content {
    padding: 3px 0 0 0;
}
</style>
